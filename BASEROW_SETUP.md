# Baserow Integration Setup Guide

This guide will help you set up Baserow integration for your blog to fetch blog posts dynamically instead of using hardcoded JSON data.

## Prerequisites

1. A Baserow account (sign up at https://baserow.io)
2. A Baserow table with your blog data

## Step 1: Create Baserow Table

Create a table in Baserow with the following fields:

### Required Fields:
- **title** (Text) - Blog post title
- **excerpt** (Long Text) - Short description/excerpt
- **content** (Long Text) - Full blog content
- **author** (Text) - Author name
- **category** (Text) - Blog category
- **published** (Boolean) - Whether the blog is published

### Optional Fields:
- **slug** (Text) - URL slug (auto-generated if not provided)
- **image** (URL or File) - Featured image
- **date** (Date) - Publication date
- **distance** (Text) - Travel distance (for travel blogs)
- **duration** (Text) - Travel duration (for travel blogs)
- **read_time** (Text) - Estimated read time

## Step 2: Get Your Baserow Credentials

1. **Table ID**: 
   - Go to your Baserow table
   - Look at the URL: `https://baserow.io/database/[DATABASE_ID]/table/[TABLE_ID]/`
   - Copy the TABLE_ID number

2. **API Token**:
   - Go to your Baserow account settings
   - Navigate to "API tokens"
   - Create a new token with read permissions
   - Copy the generated token

## Step 3: Configure the Integration

1. Open `baserow-config.js` file
2. Replace the placeholder values:

```javascript
const BASEROW_CONFIG = {
    baseUrl: 'https://api.baserow.io/api/database/rows/table/',
    tableId: 'YOUR_ACTUAL_TABLE_ID', // Replace with your table ID
    token: 'YOUR_ACTUAL_API_TOKEN',   // Replace with your API token
    
    // Update field mappings if your fields have different names
    fieldMappings: {
        title: 'title',
        excerpt: 'excerpt',
        content: 'content',
        author: 'author',
        category: 'category',
        image: 'image',
        slug: 'slug',
        date: 'date',
        published: 'published',
        distance: 'distance',
        duration: 'duration',
        read_time: 'read_time'
    }
};
```

## Step 4: Test the Integration

1. Open your blog page in a browser
2. Check the browser console for any errors
3. If successful, you should see: "✅ Successfully fetched X blog posts from Baserow"
4. If there are issues, you'll see error messages and fallback data will be used

## Troubleshooting

### Common Issues:

1. **"Baserow configuration not set up properly"**
   - Make sure you've updated the tableId and token in baserow-config.js

2. **"HTTP error! status: 401"**
   - Check your API token is correct and has proper permissions

3. **"HTTP error! status: 404"**
   - Verify your table ID is correct

4. **"Invalid response format"**
   - Check that your table exists and has data

### Field Mapping:

If your Baserow fields have different names, update the `fieldMappings` object in the config file. For example:

```javascript
fieldMappings: {
    title: 'blog_title',        // If your title field is named 'blog_title'
    excerpt: 'description',     // If your excerpt field is named 'description'
    content: 'full_content',    // If your content field is named 'full_content'
    // ... etc
}
```

## Features

- **Automatic fallback**: If Baserow is unavailable, the site will use sample data
- **Error handling**: Comprehensive error logging and user-friendly messages
- **Field mapping**: Flexible field name mapping for different table structures
- **Published filter**: Only shows blogs marked as published
- **Auto-generated slugs**: Creates URL-friendly slugs from titles if not provided
- **Image fallback**: Uses default images if none provided
- **Date formatting**: Automatically formats dates for display

## Security Notes

- Keep your API token secure and don't commit it to public repositories
- Consider using environment variables for production deployments
- The API token should have minimal required permissions (read-only for the blog table)

## Support

If you encounter issues:
1. Check the browser console for detailed error messages
2. Verify your Baserow table structure matches the expected fields
3. Test your API token and table ID using Baserow's API documentation
4. Ensure your table has at least one published blog post for testing
