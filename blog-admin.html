<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Blog Admin Dashboard | Lankadhish Cab Services</title>
    <meta name="description" content="Admin dashboard for managing Lankadhish Cab Services blog posts and content." />
    <meta name="robots" content="noindex, nofollow" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="src/assets/cablogo.png" />
    <link rel="shortcut icon" type="image/png" href="src/assets/cablogo.png" />
    <link rel="apple-touch-icon" href="src/assets/cablogo.png" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#7c3aed',
                        accent: '#f59e0b',
                        background: '#ffffff',
                        muted: '#f8fafc'
                    }
                }
            }
        }
    </script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-hero { background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%); }
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 16px 24px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .toast.show {
            transform: translateX(0);
        }
        .toast.success {
            background-color: #10b981;
        }
        .toast.error {
            background-color: #ef4444;
        }
    </style>
</head>

<body class="bg-background">
    <!-- Navigation -->
<!--     <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <img src="src/assets/cablogo.png" alt="Lankadhish" class="h-10 w-10" />
                    <span class="text-2xl font-bold text-primary">Lankadhish</span>
                    <span class="text-sm bg-primary/10 text-primary px-2 py-1 rounded-full">Admin</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition-colors">Home</a>
                    <a href="index.html#services" class="text-gray-700 hover:text-primary transition-colors">Services</a>
                    <a href="index.html#about" class="text-gray-700 hover:text-primary transition-colors">About</a>
                    <a href="index.html#contact" class="text-gray-700 hover:text-primary transition-colors">Contact</a>
                    <a href="blog.html" class="text-gray-700 hover:text-primary transition-colors">Blog</a>
                </div>
                <a href="index.html#booking" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                    Book Now
                </a>
                <!-- Mobile menu button -->

    <!-- Hero Section -->
    <section class="relative py-32 gradient-hero overflow-hidden">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Blog <span class="text-yellow-400">Admin</span>
                </h1>
                <p class="text-xl max-w-3xl mx-auto mb-8">
                    Create and manage blog posts for Lankadhish Cab Services.
                    Share travel insights and service updates with our customers.
                </p>
            </div>
        </div>
    </section>

    <!-- Admin Form Section -->
    <section class="py-20 bg-background">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-2xl shadow-xl p-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Create New Blog Post</h2>
                    
                   <form id="blog-form" class="space-y-6">
    <!-- Blog Title -->
    <div>
        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Blog Title *</label>
        <input type="text" id="title" name="title" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter blog title..." />
    </div>

    <!-- Category -->
    <div>
        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
        <input type="text" id="category" name="category" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter blog category..." />
    </div>

    <!-- Keywords -->
    <div>
        <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">Keywords (comma-separated) *</label>
        <input type="text" id="keywords" name="keywords" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="e.g., cab service, travel tips, Mumbai" />
    </div>

    <!-- Mini Card Description -->
    <div>
        <label for="miniDescription" class="block text-sm font-medium text-gray-700 mb-2">Mini Card Description *</label>
        <textarea id="miniDescription" name="miniDescription" rows="3" required
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
                  placeholder="Short description for blog card view..."></textarea>
    </div>

    <!-- Featured Image -->
    <div>
        <label for="image" class="block text-sm font-medium text-gray-700 mb-2">Featured Image *</label>
        <input type="file" id="image" name="image" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter image URL..." />
    </div>

    <!-- Author -->
    <div>
        <label for="author" class="block text-sm font-medium text-gray-700 mb-2">Author *</label>
        <input type="text" id="author" name="author" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter author name..." />
    </div>

    <!-- Date -->
    <div>
        <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date *</label>
        <input type="text" id="date" name="date" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="January 15, 2024" />
    </div>

    <!-- Read Time -->
    <div>
        <label for="readTime" class="block text-sm font-medium text-gray-700 mb-2">Read Time (in minutes) *</label>
        <input type="number" id="readTime" name="readTime" required min="1"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="e.g., 5" />
    </div>

    <!-- Slug -->
    <div>
        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
        <input type="text" id="slug" name="slug" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="e.g., gujarat-to-mumbai-cab-guide" />
    </div>

    <!-- Sub Title -->
    <div>
        <label for="subTitle" class="block text-sm font-medium text-gray-700 mb-2">Sub Title (Content Page)</label>
        <input type="text" id="subTitle" name="subTitle"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter sub title..." />
    </div>

    <!-- Starter Paragraph -->
    <div>
        <label for="starterPara" class="block text-sm font-medium text-gray-700 mb-2">Starter Paragraph (up to 3 lines) *</label>
        <textarea id="starterPara" name="starterPara" rows="3" required
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
                  placeholder="Write an engaging starter paragraph..."></textarea>
    </div>

    <!-- Section One -->
    <div>
        <label for="sectionOneHeading" class="block text-sm font-medium text-gray-700 mb-2">Section One Heading *</label>
        <input type="text" id="sectionOneHeading" name="sectionOneHeading" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="e.g., Why Choose Gujarat to Mumbai Cab" />
    </div>

    <div>
        <label for="sectionOnePoint1" class="block text-sm font-medium text-gray-700 mb-2">Section One Point 1 *</label>
        <input type="text" id="sectionOnePoint1" name="sectionOnePoint1" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter point 1..." />
    </div>

    <div>
        <label for="sectionOnePoint2" class="block text-sm font-medium text-gray-700 mb-2">Section One Point 2 *</label>
        <input type="text" id="sectionOnePoint2" name="sectionOnePoint2" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter point 2..." />
    </div>

    <div>
        <label for="sectionOnePoint3" class="block text-sm font-medium text-gray-700 mb-2">Section One Point 3 *</label>
        <input type="text" id="sectionOnePoint3" name="sectionOnePoint3" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter point 3..." />
    </div>

    <div>
        <label for="sectionOnePoint4" class="block text-sm font-medium text-gray-700 mb-2">Section One Point 4 *</label>
        <input type="text" id="sectionOnePoint4" name="sectionOnePoint4" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter point 4..." />
    </div>

    <div>
        <label for="sectionOnePoint5" class="block text-sm font-medium text-gray-700 mb-2">Section One Point 5 *</label>
        <input type="text" id="sectionOnePoint5" name="sectionOnePoint5" required
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="Enter point 5..." />
    </div>

    <!-- Section Two -->
    <div>
        <label for="sectionTwoHeading" class="block text-sm font-medium text-gray-700 mb-2">Section Two Heading</label>
        <input type="text" id="sectionTwoHeading" name="sectionTwoHeading"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="e.g., Travel Tips" />
    </div>

    <div>
        <label for="sectionTwoPara1" class="block text-sm font-medium text-gray-700 mb-2">Section Two Mini Para 1</label>
        <textarea id="sectionTwoPara1" name="sectionTwoPara1" rows="2"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
                  placeholder="Mini paragraph 1..."></textarea>
    </div>

    <div>
        <label for="sectionTwoPara2" class="block text-sm font-medium text-gray-700 mb-2">Section Two Mini Para 2</label>
        <textarea id="sectionTwoPara2" name="sectionTwoPara2" rows="2"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
                  placeholder="Mini paragraph 2..."></textarea>
    </div>

    <!-- Section Three -->
    <div>
        <label for="sectionThreeHeading" class="block text-sm font-medium text-gray-700 mb-2">Section Three Heading</label>
        <input type="text" id="sectionThreeHeading" name="sectionThreeHeading"
               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
               placeholder="e.g., Conclusion" />
    </div>

    <div>
        <label for="sectionThreePara1" class="block text-sm font-medium text-gray-700 mb-2">Section Three Mini Para 1</label>
        <textarea id="sectionThreePara1" name="sectionThreePara1" rows="2"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary"
                  placeholder="Closing remarks..."></textarea>
    </div>

    <!-- Submit -->
    <div>
        <button type="submit"
                class="w-full bg-primary text-white py-3 px-6 rounded-lg shadow-md hover:bg-primary-dark transition">
            Submit Blog
        </button>
    </div>
</form>

                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="src/assets/cablogo.png" alt="Lankadhish" class="h-8 w-8" />
                        <span class="text-xl font-bold">Lankadhish</span>
                    </div>
                    <p class="text-gray-400">Professional cab services across Gujarat and India. Safe, reliable, and affordable transportation.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                        <li><a href="index.html#about" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                        <li><a href="index.html#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Local Rides</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Outstation</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Airport Transfer</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Corporate</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li>📞 +91 9157575675</li>
                        <li>✉️ <EMAIL></li>
                        <li>📍 Gujarat, India</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Lankadhish Cab Services. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Notification -->
    <div id="toast" class="toast"></div>

    <script>
    // Toast notification function
    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type}`;
        toast.classList.add('show');
        
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // Convert file to base64
    function fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    // Form submission
    document.getElementById('blog-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        try {
            const formData = new FormData(this);
            const imageFile = formData.get('image');

            // Collect all fields into one object
            const blogData = {
                type: 'blog_upload',
                title: formData.get('title'),
                category: formData.get('category'),
                keywords: formData.get('keywords'),
                miniDescription: formData.get('miniDescription'),
                author: formData.get('author'),
                date: formData.get('date'),
                readTime: formData.get('readTime'),
                slug: formData.get('slug'),
                subTitle: formData.get('subTitle'),
                starterPara: formData.get('starterPara'),
                sectionOneHeading: formData.get('sectionOneHeading'),
                sectionOnePoint1: formData.get('sectionOnePoint1'),
                sectionOnePoint2: formData.get('sectionOnePoint2'),
                sectionOnePoint3: formData.get('sectionOnePoint3'),
                sectionOnePoint4: formData.get('sectionOnePoint4'),
                sectionOnePoint5: formData.get('sectionOnePoint5'),
                sectionTwoHeading: formData.get('sectionTwoHeading'),
                sectionTwoPara1: formData.get('sectionTwoPara1'),
                sectionTwoPara2: formData.get('sectionTwoPara2'),
                sectionThreeHeading: formData.get('sectionThreeHeading'),
                sectionThreePara1: formData.get('sectionThreePara1'),
                timestamp: new Date().toISOString(),
                source: 'lankadhish-blog-admin'
            };

            // Handle image upload as base64
            if (imageFile && imageFile.size > 0) {
                const base64Image = await fileToBase64(imageFile);
                blogData.image = {
                    name: imageFile.name,
                    size: imageFile.size,
                    type: imageFile.type,
                    data: base64Image
                };
            }

            // Send to webhook
            const response = await fetch('https://automate.axonflash.com/webhook/lankadish-blogs-form-upload', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(blogData)
            });

            if (response.ok) {
                showToast('✅ Blog uploaded successfully!', 'success');
                this.reset();

                // Redirect after 2 sec
                setTimeout(() => {
                    window.location.href = 'blog.html';
                }, 2000);
            } else {
                throw new Error('Webhook rejected the request');
            }
        } catch (error) {
            console.error('Error:', error);
            showToast('❌ Failed to upload blog. Please try again.', 'error');
        }
    });
</script>


    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Toast notification function
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = `toast ${type}`;
            toast.classList.add('show');
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // Convert file to base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

        // Form submission
        document.getElementById('blog-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submit-btn');
            const submitText = document.getElementById('submit-text');
            const submitLoading = document.getElementById('submit-loading');
            
            // Show loading state
            submitText.classList.add('hidden');
            submitLoading.classList.remove('hidden');
            submitBtn.disabled = true;
            
            try {
                const formData = new FormData(this);
                const imageFile = formData.get('image');
                
                // Prepare blog data
                const blogData = {
                    type: 'blog_generation',
                    title: formData.get('title'),
                    content: formData.get('content'),
                    keywords: formData.get('keywords'),
                    category: formData.get('category'),
                    author: formData.get('author') || 'Mr Amar Jankar',
                    timestamp: new Date().toISOString(),
                    source: 'lankadhish-blog-admin'
                };
                
                // Handle image upload
                if (imageFile && imageFile.size > 0) {
                    const base64Image = await fileToBase64(imageFile);
                    blogData.image = {
                        name: imageFile.name,
                        size: imageFile.size,
                        type: imageFile.type,
                        data: base64Image
                    };
                }
                
                // Send to n8n webhook
                const response = await fetch('https://automate.axonflash.com/webhook/blog-generation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(blogData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showToast('Blog post generated successfully! 🎉', 'success');
                    
                    // Reset form
                    this.reset();
                    
                    // Redirect to blog page after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'blog.html';
                    }, 2000);
                } else {
                    throw new Error('Failed to generate blog post');
                }
                
            } catch (error) {
                console.error('Error:', error);
                showToast('Failed to generate blog post. Please try again.', 'error');
            } finally {
                // Reset button state
                submitText.classList.remove('hidden');
                submitLoading.classList.add('hidden');
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
