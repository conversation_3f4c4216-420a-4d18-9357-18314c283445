<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title id="page-title">Blog Article | Lankadhish Cab Services</title>
    <meta name="description" id="page-description" content="Read our latest travel guide and cab service insights." />
    <meta name="keywords" id="page-keywords" content="travel blog, cab service, Gujarat travel, Lankadhish" />
    <meta name="author" content="Lankadhish Cab Services" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="src/assets/cablogo.png" />
    <link rel="shortcut icon" type="image/png" href="src/assets/cablogo.png" />
    <link rel="apple-touch-icon" href="src/assets/cablogo.png" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" id="og-title" content="Blog Article | Lankadhish Cab Services" />
    <meta property="og:description" id="og-description" content="Read our latest travel guide and cab service insights." />
    <meta property="og:type" content="article" />
    <meta property="og:url" id="og-url" content="https://lankadhish.com/blog-article.html" />
    <meta property="og:image" id="og-image" content="src/assets/cablogo.png" />
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" id="twitter-title" content="Blog Article | Lankadhish Cab Services" />
    <meta name="twitter:description" id="twitter-description" content="Read our latest travel guide and cab service insights." />
    <meta name="twitter:image" id="twitter-image" content="src/assets/cablogo.png" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#7c3aed',
                        accent: '#f59e0b',
                        background: '#ffffff',
                        muted: '#f8fafc'
                    }
                }
            }
        }
    </script>
    
    <!-- Structured Data -->
    <script type="application/ld+json" id="structured-data">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": "Blog Article",
        "description": "Read our latest travel guide and cab service insights.",
        "author": {
            "@type": "Person",
            "name": "Mr Amar Jankar"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Lankadhish Cab Services",
            "logo": {
                "@type": "ImageObject",
                "url": "https://lankadhish.com/src/assets/cablogo.png"
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://lankadhish.com/blog-article.html"
        }
    }
    </script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-hero { background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%); }
        .prose {
            max-width: none;
        }
        .prose h2 {
            color: #1e40af;
            font-size: 1.5rem;
            font-weight: 600;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        .prose h3 {
            color: #374151;
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        .prose p {
            margin-bottom: 1rem;
            line-height: 1.7;
            color: #374151;
        }
        .prose ul {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }
        .prose li {
            margin-bottom: 0.5rem;
            color: #374151;
        }
    </style>
</head>

<body class="bg-background">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <img src="src/assets/cablogo.png" alt="Lankadhish" class="h-10 w-10" />
                    <span class="text-2xl font-bold text-primary">Lankadhish</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition-colors">Home</a>
                    <a href="index.html#services" class="text-gray-700 hover:text-primary transition-colors">Services</a>
                    <a href="index.html#about" class="text-gray-700 hover:text-primary transition-colors">About</a>
                    <a href="index.html#contact" class="text-gray-700 hover:text-primary transition-colors">Contact</a>
                    <a href="blog.html" class="text-primary font-medium">Blog</a>
                </div>
                <a href="index.html#booking" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                    Book Now
                </a>
                <!-- Mobile menu button -->
                <button class="md:hidden text-gray-700" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <a href="index.html" class="block py-2 text-gray-700 hover:text-primary">Home</a>
                <a href="index.html#services" class="block py-2 text-gray-700 hover:text-primary">Services</a>
                <a href="index.html#about" class="block py-2 text-gray-700 hover:text-primary">About</a>
                <a href="index.html#contact" class="block py-2 text-gray-700 hover:text-primary">Contact</a>
                <a href="blog.html" class="block py-2 text-primary font-medium">Blog</a>
            </div>
        </div>
    </nav>

    <!-- Article Hero Section -->
    <section class="relative py-32 gradient-hero overflow-hidden">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center text-white">
                <div id="category-badge" class="inline-block bg-white/20 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                    Travel Guide
                </div>
                <h1 id="article-title" class="text-4xl md:text-5xl font-bold mb-6">
                    Loading Article...
                </h1>
                <div class="flex flex-wrap justify-center items-center space-x-6 text-white/90 mb-6">
                    <div class="flex items-center space-x-2">
                        <span id="article-author">👤 Mr Amar Jankar</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span id="article-date">📅 Loading...</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span id="article-readtime">⏱️ Loading...</span>
                    </div>
                </div>
                <div id="article-distance" class="flex justify-center items-center space-x-6 text-white/90 mb-6 hidden">
                    <span id="distance-info">📍 Loading...</span>
                    <span id="duration-info">🕒 Loading...</span>
                </div>
                <p id="article-excerpt" class="text-xl opacity-90 max-w-3xl mx-auto">
                    Loading article excerpt...
                </p>
            </div>
        </div>
    </section>

    <!-- Article Content -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <!-- Article Image -->
                <div class="mb-8">
                    <img id="article-image" src="" alt="" class="w-full h-64 md:h-96 object-cover rounded-xl shadow-lg" />
                </div>
                
                <!-- Article Content -->
                <div id="article-content" class="prose prose-lg mx-auto">
                    <p>Loading article content...</p>
                </div>
                
                <!-- Tags -->
                <div class="mt-12 pt-8 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Topics:</h3>
                    <div id="article-tags" class="flex flex-wrap gap-2">
                        <!-- Tags will be inserted here -->
                    </div>
                </div>
                
                <!-- Back to Blog -->
                <div class="mt-12 text-center">
                    <a href="blog.html" class="inline-flex items-center space-x-2 bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors">
                        <span>← Back to All Blogs</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Articles -->
    <section class="py-16 bg-muted">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <h2 class="text-3xl font-bold text-center mb-12">Related Articles</h2>
                <div id="related-articles" class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Related articles will be inserted here -->
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="src/assets/cablogo.png" alt="Lankadhish" class="h-8 w-8" />
                        <span class="text-xl font-bold">Lankadhish</span>
                    </div>
                    <p class="text-gray-400">Professional cab services across Gujarat and India. Safe, reliable, and affordable transportation.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                        <li><a href="index.html#about" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                        <li><a href="index.html#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Local Rides</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Outstation</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Airport Transfer</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Corporate</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li>📞 +91 9157575675</li>
                        <li>✉️ <EMAIL></li>
                        <li>📍 Gujarat, India</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Lankadhish Cab Services. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Blog data (same as blog.html)
        const blogPosts = [
            {
                slug: "gujarat-to-mumbai-cab-guide",
                title: "Complete Guide: Gujarat to Mumbai by Cab",
                content: `<h2>Journey to India's Financial Capital</h2>
                <p>Mumbai, the bustling financial capital of India, is a popular destination from Gujarat. This 525-kilometer journey offers scenic routes, comfortable travel options, and numerous attractions along the way.</p>
                
                <h3>Best Route Options</h3>
                <p>The most popular route is via NH48, passing through Vapi, Valsad, and Thane. This route offers excellent road conditions and multiple rest stops for a comfortable journey.</p>
                
                <h3>Travel Tips</h3>
                <p>Start early morning to avoid traffic congestion in Mumbai. Our professional drivers are familiar with Mumbai's traffic patterns and can navigate efficiently to your destination.</p>
                
                <h3>Popular Stops</h3>
                <p>Consider stopping at Vapi for breakfast, Valsad for local snacks, and enjoy the scenic coastal views near Dahanu. These stops make the journey more enjoyable and comfortable.</p>`,
                excerpt: "Discover the best routes, stops, and travel tips for your journey from Gujarat to Mumbai. Professional cab service insights included.",
                author: "Mr Amar Jankar",
                date: "January 15, 2024",
                readTime: "8 min read",
                category: "Travel Guide",
                image: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=400&fit=crop",
                keywords: ["Gujarat to Mumbai", "cab service", "travel guide"],
                metaDescription: "Complete guide for traveling from Gujarat to Mumbai by cab with professional service.",
                published: true,
                distance: "525 km",
                duration: "8-10 hours"
            }
        ];

        // Add all remaining blog posts with full content
        blogPosts.push(
            {
                slug: "gujarat-to-delhi-road-trip",
                title: "Gujarat to Delhi: Ultimate Road Trip Guide",
                content: `<h2>Epic Journey to the Capital</h2>
                <p>Delhi, India's vibrant capital, awaits with its rich history, diverse culture, and political significance. This 950-kilometer journey from Gujarat takes you through multiple states and diverse landscapes.</p>

                <h3>Route Planning</h3>
                <p>The best route is via NH48 and NH44, passing through Rajasthan. This route offers good road conditions, scenic views, and multiple dining options for a comfortable long-distance journey.</p>

                <h3>Historical Stops</h3>
                <p>Visit Udaipur for royal palaces, Ajmer for spiritual significance, and Jaipur for Pink City charm. These stops break the journey and add cultural richness to your travel experience.</p>

                <h3>Delhi Attractions</h3>
                <p>Explore Red Fort, India Gate, Qutub Minar, and Lotus Temple. Delhi offers a perfect blend of historical monuments and modern attractions for every type of traveler.</p>`,
                excerpt: "Plan your perfect road trip from Gujarat to Delhi with our comprehensive guide covering routes, attractions, and booking tips.",
                author: "Mr Amar Jankar",
                date: "January 20, 2024",
                readTime: "12 min read",
                category: "Road Trip",
                image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop",
                keywords: ["Gujarat to Delhi", "road trip", "capital city"],
                metaDescription: "Ultimate road trip guide from Gujarat to Delhi with route planning and attractions.",
                published: true,
                distance: "950 km",
                duration: "12-14 hours"
            },
            {
                slug: "gujarat-to-goa-coastal-route",
                title: "Best Route from Gujarat to Goa by Cab",
                content: `<h2>Coastal Paradise Awaits</h2>
                <p>Goa, India's beach paradise, offers sun, sand, and vibrant nightlife. This 600-kilometer coastal journey from Gujarat provides scenic beauty and comfortable travel to your tropical destination.</p>

                <h3>Scenic Coastal Route</h3>
                <p>Take the coastal highway via NH66 for breathtaking ocean views, palm-fringed beaches, and charming coastal towns. This route offers the most scenic experience for your Goa journey.</p>

                <h3>Beach Destinations</h3>
                <p>Visit North Goa for vibrant nightlife and water sports, or South Goa for peaceful beaches and luxury resorts. Each area offers unique experiences for different travel preferences.</p>

                <h3>Cultural Experiences</h3>
                <p>Explore Portuguese architecture, spice plantations, and local markets. Goa's unique blend of Indian and Portuguese cultures creates an unforgettable travel experience.</p>`,
                excerpt: "Explore the scenic coastal route from Gujarat to Goa. Tips for comfortable travel and must-visit stops along the way.",
                author: "Mr Amar Jankar",
                date: "January 25, 2024",
                readTime: "10 min read",
                category: "Coastal Route",
                image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&h=400&fit=crop",
                keywords: ["Gujarat to Goa", "coastal route", "beach destination"],
                metaDescription: "Scenic coastal route guide from Gujarat to Goa with travel tips and attractions.",
                published: true,
                distance: "600 km",
                duration: "10-12 hours"
            }
            // Add more blogs as needed...
        );

        // Function to get URL parameter
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Function to find blog by slug
        function findBlogBySlug(slug) {
            return blogPosts.find(blog => blog.slug === slug);
        }

        // Function to get related articles
        function getRelatedArticles(currentBlog, count = 3) {
            return blogPosts
                .filter(blog => blog.slug !== currentBlog.slug && blog.category === currentBlog.category)
                .slice(0, count);
        }

        // Function to render article
        function renderArticle(blog) {
            if (!blog) {
                document.getElementById('article-title').textContent = 'Article Not Found';
                document.getElementById('article-content').innerHTML = '<p>Sorry, the requested article could not be found.</p>';
                return;
            }

            // Update page title and meta tags
            document.getElementById('page-title').textContent = `${blog.title} | Lankadhish Cab Services`;
            document.getElementById('page-description').setAttribute('content', blog.excerpt);
            document.getElementById('page-keywords').setAttribute('content', blog.keywords.join(', '));

            // Update Open Graph tags
            document.getElementById('og-title').setAttribute('content', `${blog.title} | Lankadhish Cab Services`);
            document.getElementById('og-description').setAttribute('content', blog.excerpt);
            document.getElementById('og-image').setAttribute('content', blog.image);

            // Update Twitter tags
            document.getElementById('twitter-title').setAttribute('content', `${blog.title} | Lankadhish Cab Services`);
            document.getElementById('twitter-description').setAttribute('content', blog.excerpt);
            document.getElementById('twitter-image').setAttribute('content', blog.image);

            // Update structured data
            const structuredData = {
                "@context": "https://schema.org",
                "@type": "BlogPosting",
                "headline": blog.title,
                "description": blog.excerpt,
                "author": {
                    "@type": "Person",
                    "name": blog.author
                },
                "datePublished": blog.date,
                "publisher": {
                    "@type": "Organization",
                    "name": "Lankadhish Cab Services",
                    "logo": {
                        "@type": "ImageObject",
                        "url": "https://lankadhish.com/src/assets/cablogo.png"
                    }
                },
                "image": blog.image,
                "keywords": blog.keywords.join(', '),
                "articleSection": blog.category
            };
            document.getElementById('structured-data').textContent = JSON.stringify(structuredData);

            // Update article content
            document.getElementById('category-badge').textContent = blog.category;
            document.getElementById('article-title').textContent = blog.title;
            document.getElementById('article-author').textContent = `👤 ${blog.author}`;
            document.getElementById('article-date').textContent = `📅 ${blog.date}`;
            document.getElementById('article-readtime').textContent = `⏱️ ${blog.readTime}`;
            document.getElementById('article-excerpt').textContent = blog.excerpt;
            document.getElementById('article-image').src = blog.image;
            document.getElementById('article-image').alt = blog.title;
            document.getElementById('article-content').innerHTML = blog.content;

            // Show distance info if available
            if (blog.distance && blog.duration) {
                document.getElementById('article-distance').classList.remove('hidden');
                document.getElementById('distance-info').textContent = `📍 ${blog.distance}`;
                document.getElementById('duration-info').textContent = `🕒 ${blog.duration}`;
            }

            // Render tags
            const tagsContainer = document.getElementById('article-tags');
            tagsContainer.innerHTML = '';
            blog.keywords.forEach(keyword => {
                tagsContainer.innerHTML += `
                    <span class="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
                        ${keyword.trim()}
                    </span>
                `;
            });

            // Render related articles
            const relatedArticles = getRelatedArticles(blog);
            const relatedContainer = document.getElementById('related-articles');
            relatedContainer.innerHTML = '';

            relatedArticles.forEach(related => {
                relatedContainer.innerHTML += `
                    <article class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                        <img src="${related.image}" alt="${related.title}" class="w-full h-48 object-cover" />
                        <div class="p-6">
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium mb-3 inline-block">
                                ${related.category}
                            </span>
                            <h3 class="text-lg font-bold text-gray-900 mb-2 hover:text-primary transition-colors">
                                <a href="blog-article.html?slug=${related.slug}">${related.title}</a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-4">${related.excerpt.substring(0, 100)}...</p>
                            <a href="blog-article.html?slug=${related.slug}"
                               class="text-primary hover:text-primary/80 font-medium text-sm transition-colors">
                                Read More →
                            </a>
                        </div>
                    </article>
                `;
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const slug = getUrlParameter('slug');
            const blog = findBlogBySlug(slug);
            renderArticle(blog);
        });
