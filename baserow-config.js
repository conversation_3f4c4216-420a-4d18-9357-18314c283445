// Baserow API Configuration
// Replace these values with your actual Baserow details

const BASEROW_CONFIG = {
    // Your Baserow API base URL (usually https://api.baserow.io/api/database/rows/table/)
    baseUrl: 'https://database.axonflash.com/database/183/table/734/',
    
    // Your Baserow table ID (you can find this in your Baserow table URL)
    tableId: '734',
    
    // Your Baserow API token (generate this in your Baserow account settings)
    token: 'Qkh2CWT4xq5pewMnpJIWsj3uIOUrwHDU',
    
    // Optional: Custom field mappings if your Baserow fields have different names
    fieldMappings: {
        title: 'title',           // Maps to your title field
        excerpt: 'excerpt',       // Maps to your excerpt/description field
        content: 'content',       // Maps to your content field
        author: 'author',         // Maps to your author field
        category: 'category',     // Maps to your category field
        image: 'image',           // Maps to your image field
        slug: 'slug',             // Maps to your slug field
        date: 'date',             // Maps to your date field
        published: 'published',   // Maps to your published status field
        distance: 'distance',     // Maps to your distance field (optional)
        duration: 'duration',     // Maps to your duration field (optional)
        read_time: 'read_time'    // Maps to your read time field (optional)
    }
};


if (typeof module !== 'undefined' && module.exports) {
    module.exports = BASEROW_CONFIG;
}
