<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Travel Blog | Lankadhish Cab Services - Expert Travel Guides</title>
    <meta name="description" content="Explore our comprehensive travel guides for cab journeys across India. Expert tips, routes, and insights for Gujarat to Mumbai, Delhi, Goa, and more destinations." />
    <meta name="keywords" content="travel blog, cab service blog, Gujarat travel, Mumbai route, Delhi trip, Goa travel, travel guides, Lankadhish blog" />
    <meta name="author" content="Lankadhish Cab Services" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="src/assets/cablogo.png" />
    <link rel="shortcut icon" type="image/png" href="src/assets/cablogo.png" />
    <link rel="apple-touch-icon" href="src/assets/cablogo.png" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Travel Blog | Lankadhish Cab Services" />
    <meta property="og:description" content="Expert travel guides and tips for cab journeys across India from Gujarat to major destinations." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://lankadhish.com/blog.html" />
    <meta property="og:image" content="src/assets/cablogo.png" />
    
    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Travel Blog | Lankadhish Cab Services" />
    <meta name="twitter:description" content="Expert travel guides and tips for cab journeys across India." />
    <meta name="twitter:image" content="src/assets/cablogo.png" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: 'hsl(45 100% 51%)',
                        secondary: '#7c3aed',
                        accent: '#f59e0b',
                        background: '#ffffff',
                        muted: '#f8fafc'
                    }
                }
            }
        }
    </script>

    <!-- Baserow Configuration -->
    <script src="baserow-config.js"></script>
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Blog",
        "name": "Lankadhish Cab Services Travel Blog",
        "description": "Expert travel guides and tips for cab journeys across India",
        "url": "https://lankadhish.com/blog.html",
        "publisher": {
            "@type": "Organization",
            "name": "Lankadhish Cab Services",
            "logo": {
                "@type": "ImageObject",
                "url": "https://lankadhish.com/src/assets/cablogo.png"
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://lankadhish.com/blog.html"
        }
    }
    </script>
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-hero { background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%); }
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .animate-spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>

<body class="bg-background">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <img src="src/assets/cablogo.png" alt="Lankadhish" class="h-10 w-10" />
                    <span class="text-2xl font-bold text-primary">Lankadhish</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition-colors">Home</a>
                    <a href="index.html#services" class="text-gray-700 hover:text-primary transition-colors">Services</a>
                    <a href="index.html#about" class="text-gray-700 hover:text-primary transition-colors">About</a>
                    <a href="index.html#contact" class="text-gray-700 hover:text-primary transition-colors">Contact</a>
                    <a href="blog.html" class="text-primary font-medium">Blog</a>
                </div>
                <a href="index.html#booking" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                    Book Now
                </a>
                <!-- Mobile menu button -->
                <button class="md:hidden text-gray-700" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden pb-4">
                <a href="index.html" class="block py-2 text-gray-700 hover:text-primary">Home</a>
                <a href="index.html#services" class="block py-2 text-gray-700 hover:text-primary">Services</a>
                <a href="index.html#about" class="block py-2 text-gray-700 hover:text-primary">About</a>
                <a href="index.html#contact" class="block py-2 text-gray-700 hover:text-primary">Contact</a>
                <a href="blog.html" class="block py-2 text-primary font-medium">Blog</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative py-32 bg-black overflow-hidden">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Travel <span class="text-yellow-400">Blog</span>
                </h1>
                <p class="text-xl max-w-3xl mx-auto mb-8">
                    Insights, tips, and stories from the world of transportation.
                    Stay informed and travel smarter with our expert advice.
                </p>
            </div>
        </div>
    </section>

    <!-- Blog Posts Grid -->
    <section class="py-20 bg-background">
        <div class="container mx-auto px-4">
            <!-- Header with Refresh Button -->
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900">Latest Blog Posts</h2>
                    <p class="text-gray-600 mt-2">Discover travel guides and insights from our experts</p>
                </div>
                <button onclick="refreshBlogData()"
                        id="refresh-btn"
                        class="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <span>Refresh</span>
                </button>
            </div>

            <!-- Loading State -->
            <div id="loading" class="flex justify-center items-center py-20 hidden">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p class="text-gray-600">Loading blog posts from Baserow...</p>
                </div>
            </div>

            <!-- Blog Grid -->
            <div id="blog-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Blog posts will be inserted here by JavaScript -->
            </div>

            <!-- Pagination -->
            <div id="pagination" class="flex justify-center items-center space-x-2 mt-12">
                <!-- Pagination will be inserted here by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="src/assets/cablogo.png" alt="Lankadhish" class="h-8 w-8" />
                        <span class="text-xl font-bold">Lankadhish</span>
                    </div>
                    <p class="text-gray-400">Professional cab services across Gujarat and India. Safe, reliable, and affordable transportation.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                        <li><a href="index.html#about" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                        <li><a href="index.html#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Local Rides</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Outstation</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Airport Transfer</a></li>
                        <li><a href="index.html#services" class="text-gray-400 hover:text-white transition-colors">Corporate</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li>📞 +91 9157575675</li>
                        <li>✉️ <EMAIL></li>
                        <li>📍 Gujarat, India</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Lankadhish Cab Services. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Check if Baserow configuration is loaded
        function checkBaserowConfig() {
            if (typeof BASEROW_CONFIG === 'undefined') {
                console.warn('Baserow configuration not found. Please ensure baserow-config.js is loaded.');
                return false;
            }

            if (BASEROW_CONFIG.tableId === 'YOUR_TABLE_ID' || BASEROW_CONFIG.token === 'YOUR_API_TOKEN') {
                console.warn('Baserow configuration not set up. Please update baserow-config.js with your actual credentials.');
                return false;
            }

            return true;
        }

        // Global variables for blog data
        let blogPosts = [];
        let isLoading = false;
        let dataSource = 'fallback'; // 'baserow' or 'fallback'

        // Function to fetch blog data from Baserow
        async function fetchBlogDataFromBaserow() {
            try {
                isLoading = true;
                showLoading(true);

                // Check if Baserow is properly configured
                if (!checkBaserowConfig()) {
                    throw new Error('Baserow configuration not set up properly');
                }

                const response = await fetch(`${BASEROW_CONFIG.baseUrl}${BASEROW_CONFIG.tableId}/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Token ${BASEROW_CONFIG.token}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
                }

                const data = await response.json();

                if (!data.results || !Array.isArray(data.results)) {
                    throw new Error('Invalid response format from Baserow API');
                }

                // Transform Baserow data to match our blog structure
                blogPosts = data.results.map(row => {
                    const mappings = BASEROW_CONFIG.fieldMappings || {};

                    return {
                        id: row.id,
                        slug: row[mappings.slug || 'slug'] || generateSlug(row[mappings.title || 'title']),
                        title: row[mappings.title || 'title'] || 'Untitled',
                        excerpt: row[mappings.excerpt || 'excerpt'] || row.description || '',
                        author: row[mappings.author || 'author'] || 'Mr Amar Jankar',
                        date: formatDate(row[mappings.date || 'date'] || row.created_on),
                        readTime: row[mappings.read_time || 'read_time'] || calculateReadTime(row[mappings.content || 'content'] || row[mappings.excerpt || 'excerpt']),
                        category: row[mappings.category || 'category'] || 'Travel Guide',
                        image: row[mappings.image || 'image'] || row.featured_image || 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=400&fit=crop',
                        distance: row[mappings.distance || 'distance'] || '',
                        duration: row[mappings.duration || 'duration'] || '',
                        content: row[mappings.content || 'content'] || '',
                        published: row[mappings.published || 'published'] !== false // Default to true if not specified
                    };
                }).filter(blog => blog.published); // Only show published blogs

                dataSource = 'baserow';
                console.log(`✅ Successfully fetched ${blogPosts.length} blog posts from Baserow`);
                updateDataSourceIndicator();
                return blogPosts;

            } catch (error) {
                console.error('❌ Error fetching blog data from Baserow:', error);

                // Show user-friendly error message
                if (error.message.includes('configuration')) {
                    console.log('ℹ️ Please configure your Baserow credentials in baserow-config.js');
                }

                // Fallback to sample data if Baserow fails
                console.log('🔄 Using fallback sample data...');
                dataSource = 'fallback';
                blogPosts = getFallbackBlogData();
                updateDataSourceIndicator();
                return blogPosts;
            } finally {
                isLoading = false;
                showLoading(false);
            }
        }

        // Helper function to generate slug from title
        function generateSlug(title) {
            return title
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
        }

        // Helper function to format date
        function formatDate(dateString) {
            if (!dateString) return new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Helper function to calculate read time
        function calculateReadTime(content) {
            if (!content) return '5 min read';
            const wordsPerMinute = 200;
            const wordCount = content.split(/\s+/).length;
            const minutes = Math.ceil(wordCount / wordsPerMinute);
            return `${minutes} min read`;
        }

        // Show/hide loading state
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const blogGrid = document.getElementById('blog-grid');

            if (show) {
                loading.classList.remove('hidden');
                blogGrid.style.opacity = '0.5';
            } else {
                loading.classList.add('hidden');
                blogGrid.style.opacity = '1';
            }
        }

        // Fallback blog data (in case Baserow is unavailable)
        function getFallbackBlogData() {
            return [
                {
                    slug: "gujarat-to-mumbai-cab-guide",
                    title: "Complete Guide: Gujarat to Mumbai by Cab",
                    excerpt: "Discover the best routes, stops, and travel tips for your journey from Gujarat to Mumbai. Professional cab service insights included.",
                    author: "Mr Amar Jankar",
                    date: "January 15, 2024",
                    readTime: "8 min read",
                    category: "Travel Guide",
                    image: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=400&fit=crop",
                    distance: "525 km",
                    duration: "8-10 hours"
                },
                {
                    slug: "gujarat-to-delhi-road-trip",
                    title: "Gujarat to Delhi: Ultimate Road Trip Guide",
                    excerpt: "Plan your perfect road trip from Gujarat to Delhi with our comprehensive guide covering routes, attractions, and booking tips.",
                    author: "Mr Amar Jankar",
                    date: "January 20, 2024",
                    readTime: "12 min read",
                    category: "Road Trip",
                    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop",
                    distance: "950 km",
                    duration: "12-14 hours"
                },
                {
                    slug: "gujarat-to-goa-coastal-route",
                    title: "Best Route from Gujarat to Goa by Cab",
                    excerpt: "Explore the scenic coastal route from Gujarat to Goa. Tips for comfortable travel and must-visit stops along the way.",
                    author: "Mr Amar Jankar",
                    date: "January 25, 2024",
                    readTime: "10 min read",
                    category: "Coastal Route",
                    image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&h=400&fit=crop",
                    distance: "600 km",
                    duration: "10-12 hours"
                }
            ];
        }

        // Pagination variables
        let currentPage = 1;
        const blogsPerPage = 9;
        let totalPages = 1;

        // Function to render blog posts
        function renderBlogs(page = 1) {
            if (!blogPosts || blogPosts.length === 0) {
                const blogGrid = document.getElementById('blog-grid');
                blogGrid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-500 text-lg">
                            <p class="mb-4">📝 No blog posts available at the moment.</p>
                            <p class="text-sm">Please check back later or contact us if this seems like an error.</p>
                        </div>
                    </div>
                `;
                return;
            }

            const startIndex = (page - 1) * blogsPerPage;
            const endIndex = startIndex + blogsPerPage;
            const blogsToShow = blogPosts.slice(startIndex, endIndex);

            const blogGrid = document.getElementById('blog-grid');
            blogGrid.innerHTML = '';

            blogsToShow.forEach(blog => {
                const blogCard = `
                    <article class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                        <div class="relative">
                            <img src="${blog.image}" alt="${blog.title}" class="w-full h-48 object-cover"
                                 onerror="this.src='https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=400&fit=crop'" />
                            <div class="absolute top-4 left-4">
                                <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                    ${blog.category}
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <h2 class="text-xl font-bold text-gray-900 mb-3 hover:text-primary transition-colors">
                                <a href="blog-article.html?slug=${blog.slug}">${blog.title}</a>
                            </h2>
                            <p class="text-gray-600 mb-4 line-clamp-3">${blog.excerpt}</p>
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <div class="flex items-center space-x-4">
                                    <span>👤 ${blog.author}</span>
                                    <span>📅 ${blog.date}</span>
                                    <span>⏱️ ${blog.readTime}</span>
                                </div>
                            </div>
                            ${blog.distance ? `
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <span>📍 ${blog.distance}</span>
                                    <span>🕒 ${blog.duration}</span>
                                </div>
                            ` : ''}
                            <a href="blog-article.html?slug=${blog.slug}"
                               class="inline-flex items-center text-primary hover:text-primary/80 font-medium transition-colors">
                                Read More →
                            </a>
                        </div>
                    </article>
                `;
                blogGrid.innerHTML += blogCard;
            });
        }

        // Function to render pagination
        function renderPagination() {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) return; // Don't show pagination if only one page

            // Previous button
            if (currentPage > 1) {
                pagination.innerHTML += `
                    <button onclick="changePage(${currentPage - 1})"
                            class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Previous
                    </button>
                `;
            }

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                const isActive = i === currentPage;
                pagination.innerHTML += `
                    <button onclick="changePage(${i})"
                            class="px-4 py-2 ${isActive ? 'bg-primary text-white' : 'bg-white border border-gray-300 hover:bg-gray-50'} rounded-lg transition-colors">
                        ${i}
                    </button>
                `;
            }

            // Next button
            if (currentPage < totalPages) {
                pagination.innerHTML += `
                    <button onclick="changePage(${currentPage + 1})"
                            class="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Next
                    </button>
                `;
            }
        }

        // Function to change page
        function changePage(page) {
            currentPage = page;
            window.scrollTo({ top: 0, behavior: 'smooth' });
            renderBlogs(currentPage);
            renderPagination();
        }

        // Function to update data source indicator
        function updateDataSourceIndicator() {
            const refreshBtn = document.getElementById('refresh-btn');
            if (dataSource === 'baserow') {
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Baserow Connected</span>
                `;
                refreshBtn.className = 'flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors';
            } else {
                refreshBtn.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <span>Connect to Baserow</span>
                `;
                refreshBtn.className = 'flex items-center space-x-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors';
            }
        }

        // Function to refresh blog data
        async function refreshBlogData() {
            try {
                const refreshBtn = document.getElementById('refresh-btn');
                refreshBtn.disabled = true;
                refreshBtn.innerHTML = `
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Refreshing...</span>
                `;

                await fetchBlogDataFromBaserow();
                totalPages = Math.ceil(blogPosts.length / blogsPerPage);
                currentPage = 1; // Reset to first page
                renderBlogs(1);
                renderPagination();
            } catch (error) {
                console.error('Error refreshing blog data:', error);
            } finally {
                const refreshBtn = document.getElementById('refresh-btn');
                refreshBtn.disabled = false;
                updateDataSourceIndicator();
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await fetchBlogDataFromBaserow();
                totalPages = Math.ceil(blogPosts.length / blogsPerPage);
                renderBlogs(1);
                renderPagination();
                updateDataSourceIndicator();
            } catch (error) {
                console.error('Error initializing blog page:', error);
                // Fallback will be handled in fetchBlogDataFromBaserow
                totalPages = Math.ceil(blogPosts.length / blogsPerPage);
                renderBlogs(1);
                renderPagination();
                updateDataSourceIndicator();
            }
        });
    </script>
</body>
</html>
